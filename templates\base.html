<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}LandHub - Land Real Estate Platform{% endblock %}</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3.7.2/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3.7.2/unpoly.min.css">
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <style>
        [x-cloak] { display: none !important; }
        
        /* ShadCN-inspired button styles */
        .btn-primary {
            @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
        }
        
        .btn-secondary {
            @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
        }
        
        .btn-danger {
            @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
        }
        
        /* Form input styles */
        .form-input {
            @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
        }
        
        /* Alert styles */
        .alert {
            @apply p-4 rounded-md mb-4;
        }
        
        .alert-success {
            @apply bg-green-50 border border-green-200 text-green-800;
        }
        
        .alert-error {
            @apply bg-red-50 border border-red-200 text-red-800;
        }
        
        .alert-warning {
            @apply bg-yellow-50 border border-yellow-200 text-yellow-800;
        }
        
        .alert-info {
            @apply bg-blue-50 border border-blue-200 text-blue-800;
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{% url 'home' %}" class="text-xl font-bold text-primary-600">
                        LandHub
                    </a>
                </div>
                
                <div class="flex items-center space-x-4">
                    {% if user.is_authenticated %}
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <span class="mr-2">{{ user.first_name|default:user.username }}</span>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            
                            <div x-show="open" @click.away="open = false" x-cloak
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                <a href="{% url 'auth:profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                                <a href="{% url 'auth:dashboard_redirect' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Dashboard</a>
                                <hr class="my-1">
                                <a href="{% url 'auth:logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</a>
                            </div>
                        </div>
                    {% else %}
                        <a href="{% url 'auth:login' %}" class="text-gray-700 hover:text-primary-600">Login</a>
                        <a href="{% url 'auth:register' %}" class="btn-primary">Register</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags|default:'info' }}" x-data="{ show: true }" x-show="show">
                    <div class="flex justify-between items-center">
                        <span>{{ message }}</span>
                        <button @click="show = false" class="ml-4 text-gray-400 hover:text-gray-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main class="{% block main_class %}max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8{% endblock %}">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-auto">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center text-gray-600">
                <p>&copy; 2025 LandHub. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- HTMX Configuration -->
    <script>
        // Configure HTMX
        document.body.addEventListener('htmx:configRequest', function(evt) {
            evt.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
        });
        
        // Show loading states
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            const target = evt.target;
            if (target.classList.contains('btn-primary') || target.classList.contains('btn-secondary')) {
                target.classList.add('opacity-50', 'cursor-not-allowed');
                target.disabled = true;
            }
        });
        
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            const target = evt.target;
            if (target.classList.contains('btn-primary') || target.classList.contains('btn-secondary')) {
                target.classList.remove('opacity-50', 'cursor-not-allowed');
                target.disabled = false;
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>