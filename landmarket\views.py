from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages


def home(request):
    """Home page view"""
    return render(request, 'home.html')


@login_required
def admin_dashboard(request):
    """Admin dashboard view"""
    if not request.user.profile.role == 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('auth:dashboard_redirect')
    return render(request, 'dashboards/admin_dashboard.html')


@login_required
def seller_dashboard(request):
    """Seller dashboard view"""
    if not request.user.profile.role == 'seller':
        messages.error(request, 'Access denied. Seller privileges required.')
        return redirect('auth:dashboard_redirect')
    return render(request, 'dashboards/seller_dashboard.html')


@login_required
def buyer_dashboard(request):
    """Buyer dashboard view"""
    if not request.user.profile.role == 'buyer':
        messages.error(request, 'Access denied. Buyer privileges required.')
        return redirect('auth:dashboard_redirect')
    return render(request, 'dashboards/buyer_dashboard.html')
