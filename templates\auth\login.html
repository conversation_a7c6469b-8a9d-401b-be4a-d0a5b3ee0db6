{% extends 'base.html' %}

{% block title %}Login - LandHub{% endblock %}

{% block content %}
<div class="max-w-md mx-auto bg-white rounded-lg shadow-sm border p-8">
    <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-gray-900">Sign In</h1>
        <p class="text-gray-600 mt-2">Welcome back to LandHub</p>
    </div>
    
    <form method="post" hx-post="{% url 'auth:login' %}" hx-target="#form-container" hx-swap="outerHTML">
        <div id="form-container">
            {% csrf_token %}
            
            <div class="mb-4">
                <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Username
                </label>
                {{ form.username }}
                {% if form.username.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.username.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-6">
                <label for="{{ form.password.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Password
                </label>
                {{ form.password }}
                {% if form.password.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.password.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <input type="checkbox" name="remember_me" id="remember_me" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                    <label for="remember_me" class="ml-2 block text-sm text-gray-700">
                        Remember me
                    </label>
                </div>
                <a href="{% url 'auth:password_reset' %}" class="text-sm text-primary-600 hover:text-primary-500">
                    Forgot password?
                </a>
            </div>
            
            <button type="submit" class="btn-primary w-full">
                Sign In
            </button>
        </div>
    </form>
    
    <div class="mt-6 text-center">
        <p class="text-sm text-gray-600">
            Don't have an account?
            <a href="{% url 'auth:register' %}" class="text-primary-600 hover:text-primary-500 font-medium">
                Sign up
            </a>
        </p>
    </div>
</div>
{% endblock %}