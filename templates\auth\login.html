{% extends 'base.html' %}

{% block title %}Sign In - LandHub{% endblock %}

{% block main_class %}flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8{% endblock %}

{% block content %}
<div class="w-full max-w-md space-y-8">
    <!-- Header -->
    <div class="text-center">
        <div class="flex justify-center mb-6">
            <div class="w-16 h-16 bg-gradient-primary rounded-xl flex items-center justify-center shadow-lg">
                <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
            </div>
        </div>
        <h1 class="text-3xl font-display font-bold text-secondary-900">Welcome back</h1>
        <p class="mt-2 text-secondary-600">Sign in to your LandHub account</p>
    </div>

    <!-- Login Form Card -->
    <div class="card card-elevated">
        <div class="card-body">
            <form method="post"
                  hx-post="{% url 'auth:login' %}"
                  hx-target="#form-container"
                  hx-swap="outerHTML"
                  x-data="{ loading: false }"
                  @htmx:before-request="loading = true"
                  @htmx:after-request="loading = false">
                <div id="form-container">
                    {% csrf_token %}

                    <!-- Username Field -->
                    <div class="mb-6">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Username or Email
                        </label>
                        <div class="relative">
                            {{ form.username|add_class:"form-input pl-10" }}
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="w-5 h-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        </div>
                        {% if form.username.errors %}
                            <div class="form-error">
                                {% for error in form.username.errors %}
                                    <div class="flex items-center mt-1">
                                        <svg class="w-4 h-4 mr-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>{{ error }}</span>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Password Field -->
                    <div class="mb-6">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            Password
                        </label>
                        <div class="relative" x-data="{ showPassword: false }">
                            <input :type="showPassword ? 'text' : 'password'"
                                   name="{{ form.password.name }}"
                                   id="{{ form.password.id_for_label }}"
                                   class="form-input pl-10 pr-10"
                                   placeholder="Enter your password"
                                   required>
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="w-5 h-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                            </div>
                            <button type="button"
                                    @click="showPassword = !showPassword"
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center text-secondary-400 hover:text-secondary-600 focus:outline-none">
                                <svg x-show="!showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <svg x-show="showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                </svg>
                            </button>
                        </div>
                        {% if form.password.errors %}
                            <div class="form-error">
                                {% for error in form.password.errors %}
                                    <div class="flex items-center mt-1">
                                        <svg class="w-4 h-4 mr-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>{{ error }}</span>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center">
                            <input type="checkbox"
                                   name="remember_me"
                                   id="remember_me"
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded transition-colors duration-200">
                            <label for="remember_me" class="ml-2 block text-sm text-secondary-700 hover:text-secondary-900 cursor-pointer">
                                Remember me
                            </label>
                        </div>
                        <a href="{% url 'auth:password_reset' %}"
                           class="text-sm text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200">
                            Forgot password?
                        </a>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit"
                            class="btn btn-primary btn-lg w-full"
                            :class="{ 'loading': loading }"
                            :disabled="loading">
                        <span x-show="!loading">Sign In</span>
                        <span x-show="loading" class="flex items-center justify-center">
                            <div class="spinner w-4 h-4 mr-2"></div>
                            Signing in...
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Sign Up Link -->
    <div class="text-center">
        <p class="text-sm text-secondary-600">
            Don't have an account?
            <a href="{% url 'auth:register' %}"
               class="font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200">
                Create your account
            </a>
        </p>
    </div>

    <!-- Social Login (Future Enhancement) -->
    <div class="mt-6">
        <div class="relative">
            <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-secondary-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-gradient-to-br from-secondary-50 to-secondary-100 text-secondary-500">Or continue with</span>
            </div>
        </div>

        <div class="mt-6 grid grid-cols-2 gap-3">
            <button class="btn btn-secondary btn-md w-full" disabled>
                <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                </svg>
                Google
            </button>
            <button class="btn btn-secondary btn-md w-full" disabled>
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                Facebook
            </button>
        </div>
    </div>
</div>
{% endblock %}