from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate
from django.contrib.auth.views import LoginView, LogoutView
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import CreateView
from .forms import CustomUserCreationForm, UserProfileForm


class CustomLoginView(LoginView):
    template_name = 'auth/login.html'
    redirect_authenticated_user = True
    
    def get_success_url(self):
        """Redirect users based on their role"""
        user = self.request.user
        if hasattr(user, 'profile'):
            role = user.profile.role
            if role == 'admin':
                return '/admin-dashboard/'
            elif role == 'seller':
                return '/seller-dashboard/'
            else:  # buyer
                return '/buyer-dashboard/'
        return '/dashboard/'
    
    def form_invalid(self, form):
        messages.error(self.request, 'Invalid username or password.')
        return super().form_invalid(form)


class CustomLogoutView(LogoutView):
    next_page = '/'
    
    def dispatch(self, request, *args, **kwargs):
        messages.success(request, 'You have been successfully logged out.')
        return super().dispatch(request, *args, **kwargs)


class RegisterView(CreateView):
    form_class = CustomUserCreationForm
    template_name = 'auth/register.html'
    success_url = reverse_lazy('auth:login')
    
    def form_valid(self, form):
        response = super().form_valid(form)
        username = form.cleaned_data.get('username')
        messages.success(self.request, f'Account created for {username}! You can now log in.')
        return response
    
    def form_invalid(self, form):
        for field, errors in form.errors.items():
            for error in errors:
                messages.error(self.request, f'{field}: {error}')
        return super().form_invalid(form)


@login_required
def profile_view(request):
    """View and edit user profile"""
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=request.user.profile)
        if form.is_valid():
            form.save()
            messages.success(request, 'Profile updated successfully!')
            return redirect('auth:profile')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = UserProfileForm(instance=request.user.profile)
    
    return render(request, 'auth/profile.html', {'form': form})


@login_required
def dashboard_redirect(request):
    """Redirect users to their role-specific dashboard"""
    user = request.user
    if hasattr(user, 'profile'):
        role = user.profile.role
        if role == 'admin':
            return redirect('admin_dashboard')
        elif role == 'seller':
            return redirect('seller_dashboard')
        else:  # buyer
            return redirect('buyer_dashboard')
    return redirect('buyer_dashboard')  # Default fallback