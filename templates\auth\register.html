{% extends 'base.html' %}

{% block title %}Register - LandHub{% endblock %}

{% block content %}
<div class="max-w-md mx-auto bg-white rounded-lg shadow-sm border p-8">
    <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-gray-900">Create Account</h1>
        <p class="text-gray-600 mt-2">Join LandHub today</p>
    </div>
    
    <form method="post" hx-post="{% url 'auth:register' %}" hx-target="#form-container" hx-swap="outerHTML">
        <div id="form-container">
            {% csrf_token %}
            
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        First Name
                    </label>
                    {{ form.first_name }}
                    {% if form.first_name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.first_name.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Last Name
                    </label>
                    {{ form.last_name }}
                    {% if form.last_name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.last_name.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="mb-4">
                <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Username
                </label>
                {{ form.username }}
                {% if form.username.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.username.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-4">
                <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Email
                </label>
                {{ form.email }}
                {% if form.email.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.email.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-4">
                <label for="{{ form.role.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Role
                </label>
                {{ form.role }}
                {% if form.role.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.role.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-4">
                <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Phone (Optional)
                </label>
                {{ form.phone }}
                {% if form.phone.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.phone.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-4">
                <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Password
                </label>
                {{ form.password1 }}
                {% if form.password1.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.password1.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-6">
                <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Confirm Password
                </label>
                {{ form.password2 }}
                {% if form.password2.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.password2.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <button type="submit" class="btn-primary w-full">
                Create Account
            </button>
        </div>
    </form>
    
    <div class="mt-6 text-center">
        <p class="text-sm text-gray-600">
            Already have an account?
            <a href="{% url 'auth:login' %}" class="text-primary-600 hover:text-primary-500 font-medium">
                Sign in
            </a>
        </p>
    </div>
</div>
{% endblock %}