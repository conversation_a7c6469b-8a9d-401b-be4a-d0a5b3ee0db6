{% extends 'base.html' %}

{% block title %}Profile - LandHub{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm border p-8">
        <div class="flex items-center justify-between mb-8">
            <h1 class="text-2xl font-bold text-gray-900">Profile Settings</h1>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800">
                {{ user.profile.get_role_display }}
            </span>
        </div>
        
        <form method="post" enctype="multipart/form-data" hx-post="{% url 'auth:profile' %}" hx-target="#form-container" hx-swap="outerHTML">
            <div id="form-container">
                {% csrf_token %}
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            First Name
                        </label>
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.first_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Last Name
                        </label>
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.last_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Email
                    </label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.email.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Phone
                    </label>
                    {{ form.phone }}
                    {% if form.phone.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.phone.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <label for="{{ form.bio.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Bio
                    </label>
                    {{ form.bio }}
                    {% if form.bio.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.bio.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div class="mb-6">
                    <label for="{{ form.avatar.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Avatar
                    </label>
                    {% if user.profile.avatar %}
                        <div class="mb-2">
                            <img src="{{ user.profile.avatar.url }}" alt="Current avatar" class="w-16 h-16 rounded-full object-cover">
                        </div>
                    {% endif %}
                    {{ form.avatar }}
                    {% if form.avatar.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.avatar.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div class="flex justify-between">
                    <a href="{% url 'auth:dashboard_redirect' %}" class="btn-secondary">
                        Back to Dashboard
                    </a>
                    <button type="submit" class="btn-primary">
                        Update Profile
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}